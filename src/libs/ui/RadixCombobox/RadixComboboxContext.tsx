import { createContext, useContext } from 'react';

export interface RadixComboboxContextType {
  isOpen: boolean;
  selectedValue?: unknown;
  options: unknown[];
  inputValue: string;
  highlightedIndex: number;
  openCombobox: () => void;
  closeCombobox: () => void;
  selectOption: (value: unknown) => void;
  setInputValue: (value: string) => void;
  registerOption: (value: unknown, element: HTMLElement | null) => void;
  unregisterOption: (value: unknown) => void;
  getOptionValue?: (option: unknown) => string | number;
  isLoading?: boolean;
  navigateOptions: (direction: 'up' | 'down') => void;
  selectHighlighted: () => void;
  resetHighlight: () => void;
  recreateOptionsFromChildren: (childrenOptions: unknown[]) => void;
}

export const RadixComboboxContext = createContext<RadixComboboxContextType | null>(null);

export function useRadixComboboxContext() {
  const context = useContext(RadixComboboxContext);
  if (!context) {
    throw new Error('RadixCombobox components must be used within a RadixCombobox');
  }
  return context;
}
