import { type ReactNode, useEffect, Children, isValidElement } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import * as Command from '@radix-ui/react-command';
import * as Popover from '@radix-ui/react-popover';
import { useRadixComboboxContext } from '../../RadixComboboxContext';
import { mergeClasses } from '@/utils';

const comboboxOptionsVariants = cva(
  'absolute z-10 mt-1 overflow-auto bg-white w-full',
  {
    variants: {
      variant: {
        default: 'rounded-lg shadow-md',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

interface RadixComboboxOptionsProps
  extends VariantProps<typeof comboboxOptionsVariants> {
  children: ReactNode;
  className?: string;
}

export const RadixComboboxOptions = ({
  children,
  className,
  variant,
}: RadixComboboxOptionsProps) => {
  const { isOpen, recreateOptionsFromChildren } = useRadixComboboxContext();

  useEffect(() => {
    if (isOpen && children) {
      const childrenArray = Children.toArray(children);
      const optionValues = childrenArray
        .filter(
          (child) => isValidElement(child) && child.props?.value !== undefined,
        )
        .map((child) => (isValidElement(child) ? child.props.value : undefined))
        .filter((value) => value !== undefined);

      recreateOptionsFromChildren(optionValues);
    }
  }, [children, isOpen, recreateOptionsFromChildren]);

  if (!isOpen) return null;

  const hasNoChildren =
    !children || (Array.isArray(children) && children.length === 0);

  if (hasNoChildren) return null;

  return (
    <Popover.Portal>
      <Popover.Content
        className={mergeClasses(comboboxOptionsVariants({ variant, className }))}
        sideOffset={4}
        align="start"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <Command.List>
          {children}
        </Command.List>
      </Popover.Content>
    </Popover.Portal>
  );
};
