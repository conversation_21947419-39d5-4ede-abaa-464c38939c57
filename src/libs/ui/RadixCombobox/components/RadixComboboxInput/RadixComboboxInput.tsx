import {
  useRef,
  type ChangeEvent,
  type KeyboardEvent,
  type ReactNode,
} from 'react';
import { Loader } from '@mantine/core';
import * as Command from '@radix-ui/react-command';
import * as Popover from '@radix-ui/react-popover';
import { useRadixComboboxContext } from '../../RadixComboboxContext';
import { mergeClasses } from '@/utils';
import { Flex } from '@/libs/ui/Flex/Flex';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';

interface RadixComboboxInputProps {
  placeholder?: string;
  className?: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onEnterPress?: (value: string) => void;
  displayValue?: (value: unknown) => string;
  label?: string | ReactNode;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
  mask?: (value: string) => string;
  tooltip?: string;
}

export function RadixComboboxInput({
  placeholder,
  className,
  onChange,
  onEnterPress,
  displayValue,
  label,
  error,
  size,
  align,
  mask,
  tooltip,
}: RadixComboboxInputProps) {
  const {
    isOpen,
    selectedValue,
    inputValue,
    openCombobox,
    setInputValue,
    closeCombobox,
    isLoading,
    navigateOptions,
    selectHighlighted,
    resetHighlight,
    highlightedIndex,
  } = useRadixComboboxContext();

  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    onChange?.(event);
    if (!isOpen) {
      openCombobox();
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (isOpen && highlightedIndex >= 0) {
          selectHighlighted();
        } else {
          const currentValue = inputValue.trim();
          if (currentValue && onEnterPress) {
            onEnterPress(currentValue);
          }
        }
        closeCombobox();
        break;

      case 'ArrowDown':
        event.preventDefault();
        navigateOptions('down');
        break;

      case 'ArrowUp':
        navigateOptions('up');
        break;

      case 'Escape':
        event.preventDefault();
        closeCombobox();
        resetHighlight();
        break;
    }
  };

  const handleFocus = () => {
    openCombobox();
  };

  const displayedValue = (() => {
    if (isOpen && inputValue.trim()) {
      return inputValue;
    }

    if (selectedValue && displayValue) {
      return displayValue(selectedValue);
    }

    return inputValue;
  })();

  const inputId = `input-${Math.random().toString(36).substr(2, 9)}`;

  const inputVariants = {
    sm: 'h-8 px-2 text-sm',
    md: 'h-10 px-3 text-sm',
    lg: 'h-12 px-4 text-base',
  };

  const sizeClass = inputVariants[size || 'md'];

  return (
    <Popover.Trigger asChild>
      <div className="relative flex w-full flex-col">
        {label || tooltip ? (
          <Flex gap="0.5rem" align="center" mb="0.4rem">
            {label && (
              <label htmlFor={inputId} className="text-sm">
                {label}
              </label>
            )}
            {tooltip && <HelpTooltip message={tooltip} />}
          </Flex>
        ) : null}
        <div className="relative">
          <Command.Input
            id={inputId}
            ref={inputRef}
            value={displayedValue}
            placeholder={placeholder}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            style={{ textAlign: align }}
            className={mergeClasses(
              'box-border w-full rounded-md border border-gray-300 bg-white font-sans transition-colors duration-150 ease-in-out',
              'focus-visible:outline focus-visible:outline-2 focus-visible:outline-yellow-400 focus-visible:outline-offset-2',
              'disabled:bg-gray-100',
              error && 'border-red-500',
              sizeClass,
              className,
            )}
            aria-invalid={!!error}
          />
          {isLoading && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <Loader size="sm" />
            </div>
          )}
        </div>
        {error && <p className="mt-1 text-xs text-red-600">{error}</p>}
      </div>
    </Popover.Trigger>
  );
}
