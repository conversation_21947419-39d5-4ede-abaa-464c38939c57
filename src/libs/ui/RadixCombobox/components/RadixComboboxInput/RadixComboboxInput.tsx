import {
  useRef,
  type ChangeEvent,
  type KeyboardEvent,
  type ReactNode,
} from 'react';
import { Loader } from '@mantine/core';
import * as Popover from '@radix-ui/react-popover';
import { Input } from '../../../../form/Input';
import { useRadixComboboxContext } from '../../RadixComboboxContext';

interface RadixComboboxInputProps {
  placeholder?: string;
  className?: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onEnterPress?: (value: string) => void;
  displayValue?: (value: unknown) => string;
  label?: string | ReactNode;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
  mask?: (value: string) => string;
  tooltip?: string;
}

export function RadixComboboxInput({
  placeholder,
  className,
  onChange,
  onEnterPress,
  displayValue,
  label,
  error,
  size,
  align,
  mask,
  tooltip,
}: RadixComboboxInputProps) {
  const {
    isOpen,
    selectedValue,
    inputValue,
    openCombobox,
    setInputValue,
    closeCombobox,
    isLoading,
    navigateOptions,
    selectHighlighted,
    resetHighlight,
    highlightedIndex,
  } = useRadixComboboxContext();

  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    onChange?.(event);
    if (!isOpen) {
      openCombobox();
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (isOpen && highlightedIndex >= 0) {
          selectHighlighted();
        } else {
          const currentValue = inputValue.trim();
          if (currentValue && onEnterPress) {
            onEnterPress(currentValue);
          }
        }
        break;

      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          openCombobox();
        }
        navigateOptions('down');
        break;

      case 'ArrowUp':
        event.preventDefault();
        if (!isOpen) {
          openCombobox();
        }
        navigateOptions('up');
        break;

      case 'Tab':
        // Allow tab to navigate through options when open
        if (isOpen) {
          event.preventDefault();
          navigateOptions('down');
        }
        break;

      case 'Escape':
        event.preventDefault();
        closeCombobox();
        resetHighlight();
        break;
    }
  };

  const handleFocus = () => {
    openCombobox();
  };

  const displayedValue = (() => {
    if (isOpen && inputValue.trim()) {
      return inputValue;
    }

    if (selectedValue && displayValue) {
      return displayValue(selectedValue);
    }

    return inputValue;
  })();

  return (
    <Popover.Trigger asChild>
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={displayedValue}
          placeholder={placeholder}
          className={className}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          label={label}
          error={error}
          size={size}
          align={align}
          mask={mask}
          tooltip={tooltip}
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <Loader size="sm" />
          </div>
        )}
      </div>
    </Popover.Trigger>
  );
}
