import { useRef, useEffect, type ReactNode } from 'react';
import * as Command from '@radix-ui/react-command';
import { useRadixComboboxContext } from '../../RadixComboboxContext';
import { mergeClasses } from '@/utils';

interface RadixComboboxOptionProps {
  value: unknown;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function RadixComboboxOption({
  value,
  children,
  className,
  disabled = false,
}: RadixComboboxOptionProps) {
  const {
    selectOption,
    registerOption,
    unregisterOption,
    getOptionValue,
    options,
    highlightedIndex,
  } = useRadixComboboxContext();

  const optionRef = useRef<HTMLDivElement>(null);

  const isHighlighted = (() => {
    if (highlightedIndex < 0 || highlightedIndex >= options.length) {
      return false;
    }

    const currentIndex = options.findIndex((option) => {
      if (!getOptionValue) {
        return option === value;
      }
      try {
        return getOptionValue(option) === getOptionValue(value);
      } catch {
        return option === value;
      }
    });

    return currentIndex === highlightedIndex && currentIndex >= 0;
  })();

  useEffect(() => {
    registerOption(value, optionRef.current);
    return () => unregisterOption(value);
  }, [value, registerOption, unregisterOption]);

  useEffect(() => {
    if (isHighlighted && optionRef.current) {
      optionRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [isHighlighted]);

  const handleSelect = () => {
    if (!disabled) {
      selectOption(value);
    }
  };

  // Convert value to string for Command.Item
  const stringValue = typeof value === 'string' ? value : String(value);

  return (
    <Command.Item
      ref={optionRef}
      value={stringValue}
      onSelect={handleSelect}
      disabled={disabled}
      className={mergeClasses(
        'cursor-pointer rounded px-3 py-2 break-words transition-colors duration-150 ease-in-out select-none',
        'hover:text-blue-700',
        'data-[selected]:text-blue-700',
        isHighlighted && 'text-blue-700',
        disabled && 'cursor-not-allowed opacity-50',
        className,
      )}
      data-highlighted={isHighlighted}
      data-disabled={disabled}
    >
      {children}
    </Command.Item>
  );
}
