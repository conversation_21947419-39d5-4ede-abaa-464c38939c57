import { useRef, useEffect, type ReactNode } from 'react';
import { Command } from 'cmdk';
import { useRadixComboboxContext } from '../../RadixComboboxContext';
import { mergeClasses } from '@/utils';

interface RadixComboboxOptionProps {
  value: unknown;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function RadixComboboxOption({
  value,
  children,
  className,
  disabled = false,
}: RadixComboboxOptionProps) {
  const {
    selectOption,
    registerOption,
    unregisterOption,
  } = useRadixComboboxContext();

  const optionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    registerOption(value, optionRef.current);
    return () => unregisterOption(value);
  }, [value, registerOption, unregisterOption]);

  const handleSelect = () => {
    if (!disabled) {
      selectOption(value);
    }
  };

  // Convert value to string for Command.Item
  const stringValue = typeof value === 'string' ? value : String(value);

  return (
    <Command.Item
      ref={optionRef}
      value={stringValue}
      onSelect={handleSelect}
      disabled={disabled}
      className={mergeClasses(
        'cursor-pointer rounded px-3 py-2 break-words transition-colors duration-150 ease-in-out select-none',
        'hover:text-blue-700',
        'data-[selected]:text-blue-700',
        'aria-selected:text-blue-700',
        disabled && 'cursor-not-allowed opacity-50',
        className,
      )}
      data-disabled={disabled}
    >
      {children}
    </Command.Item>
  );
}
