import {
  useCallback,
  useEffect,
  useRef,
  useState,
  type ReactNode,
} from 'react';
import { Command } from 'cmdk';
import * as Popover from '@radix-ui/react-popover';
import {
  RadixComboboxContext,
  type RadixComboboxContextType,
} from './RadixComboboxContext';
import { RadixComboboxInput } from './components/RadixComboboxInput/RadixComboboxInput';
import { RadixComboboxOption } from './components/RadixComboboxOption/RadixComboboxOption';
import { RadixComboboxOptions } from './components/RadixComboboxOptions/RadixComboboxOptions';

interface RadixComboboxProps {
  value?: unknown;
  onChange?: (value: unknown) => void;
  children: ReactNode;
  disabled?: boolean;
  getOptionValue?: (option: unknown) => string | number;
  isLoading?: boolean;
}

export function RadixCombobox({
  value,
  onChange,
  children,
  disabled = false,
  getOptionValue,
  isLoading = false,
}: RadixComboboxProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const optionRefs = useRef<Map<unknown, HTMLElement | null>>(new Map());
  const [optionOrder, setOptionOrder] = useState<unknown[]>([]);

  const openCombobox = useCallback(() => {
    if (!disabled) {
      setIsOpen(true);
    }
  }, [disabled]);

  const closeCombobox = useCallback(() => {
    setIsOpen(false);
    setHighlightedIndex(-1);
  }, []);

  const selectOption = useCallback(
    (optionValue: unknown) => {
      onChange?.(optionValue);
      setInputValue('');
      closeCombobox();
    },
    [onChange, closeCombobox],
  );

  const navigateOptions = useCallback(
    (direction: 'up' | 'down') => {
      if (optionOrder.length === 0) return;

      setHighlightedIndex((prevIndex) => {
        if (direction === 'down') {
          return prevIndex < optionOrder.length - 1 ? prevIndex + 1 : 0;
        } else {
          return prevIndex > 0 ? prevIndex - 1 : optionOrder.length - 1;
        }
      });
    },
    [optionOrder],
  );

  const selectHighlighted = useCallback(() => {
    if (highlightedIndex >= 0 && highlightedIndex < optionOrder.length) {
      const highlightedOption = optionOrder[highlightedIndex];
      selectOption(highlightedOption);
    }
  }, [highlightedIndex, optionOrder, selectOption]);

  const resetHighlight = useCallback(() => {
    setHighlightedIndex(-1);
  }, []);

  const recreateOptionsFromChildren = useCallback(
    (childrenOptions: unknown[]) => {
      setOptionOrder([]);
      optionRefs.current.clear();
      setHighlightedIndex(-1);
      setOptionOrder(childrenOptions);
    },
    [],
  );

  const registerOption = useCallback(
    (optionValue: unknown, element: HTMLElement | null) => {
      optionRefs.current.set(optionValue, element);
    },
    [],
  );

  const unregisterOption = useCallback((optionValue: unknown) => {
    optionRefs.current.delete(optionValue);
  }, []);

  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: Event) => {
      const target = event.target as Element;
      if (!target.closest('[data-radix-combobox]')) {
        closeCombobox();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, closeCombobox]);

  useEffect(() => {
    const refs = optionRefs.current;
    return () => {
      refs.clear();
    };
  }, []);

  useEffect(() => {
    if (isOpen) {
      setHighlightedIndex(-1);
    } else {
      setOptionOrder([]);
      optionRefs.current.clear();
    }
  }, [isOpen]);

  useEffect(() => {
    setHighlightedIndex(-1);
  }, [optionOrder]);

  const contextValue: RadixComboboxContextType = {
    isOpen,
    selectedValue: value,
    options: optionOrder,
    inputValue,
    highlightedIndex,
    openCombobox,
    closeCombobox,
    selectOption,
    setInputValue,
    registerOption,
    unregisterOption,
    getOptionValue,
    isLoading,
    navigateOptions,
    selectHighlighted,
    resetHighlight,
    recreateOptionsFromChildren,
  };

  return (
    <RadixComboboxContext.Provider value={contextValue}>
      <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
        <div data-radix-combobox className="relative w-full">
          {children}
        </div>
      </Popover.Root>
    </RadixComboboxContext.Provider>
  );
}

RadixCombobox.Input = RadixComboboxInput;
RadixCombobox.Options = RadixComboboxOptions;
RadixCombobox.Option = RadixComboboxOption;
