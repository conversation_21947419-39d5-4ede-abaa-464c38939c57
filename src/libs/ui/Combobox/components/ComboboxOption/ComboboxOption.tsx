import { useRef, useEffect, type MouseEvent, type ReactNode } from 'react';
import { useComboboxContext } from '../../ComboboxContext';
import { mergeClasses } from '@/utils';

interface ComboboxOptionProps {
  value: unknown;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function ComboboxOption({
  value,
  children,
  className,
  disabled = false,
}: ComboboxOptionProps) {
  const {
    selectOption,
    registerOption,
    unregisterOption,
    getOptionValue,
    options,
    highlightedIndex,
  } = useComboboxContext();

  const optionRef = useRef<HTMLDivElement>(null);

  const isHighlighted = (() => {
    if (highlightedIndex < 0 || highlightedIndex >= options.length) {
      return false;
    }

    const currentIndex = options.findIndex((option) => {
      if (!getOptionValue) {
        return option === value;
      }
      try {
        return getOptionValue(option) === getOptionValue(value);
      } catch {
        return option === value;
      }
    });

    return currentIndex === highlightedIndex && currentIndex >= 0;
  })();

  useEffect(() => {
    registerOption(value, optionRef.current);
    return () => unregisterOption(value);
  }, [value, registerOption, unregisterOption]);

  useEffect(() => {
    if (isHighlighted && optionRef.current) {
      optionRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [isHighlighted]);

  const handleClick = (event: MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!disabled) {
      selectOption(value);
    }
  };

  return (
    <div
      ref={optionRef}
      className={mergeClasses(
        'cursor-pointer rounded px-3 py-2 break-words transition-colors duration-150 ease-in-out select-none',
        'hover:text-blue-700',
        isHighlighted && 'text-blue-700',
        disabled && 'cursor-not-allowed opacity-50',
        className,
      )}
      onClick={handleClick}
      data-highlighted={isHighlighted}
      data-disabled={disabled}
    >
      {children}
    </div>
  );
}
