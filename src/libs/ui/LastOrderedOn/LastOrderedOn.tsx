import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { Icon } from '@/libs/icons/Icon';
import { mergeClasses } from '@/utils';
import { cva } from 'class-variance-authority';
import dayjs from 'dayjs';

const text = cva('text-xs', {
  variants: {
    size: {
      sxs: 'text-sxs ml-2',
      xs: 'text-xs ml-4',
    },
  },
  defaultVariants: {
    size: 'xs',
  },
});

export const LastOrderedOn = ({
  lastOrderedQuantity,
  lastOrderedAt,
  size = 'xs',
}: {
  lastOrderedQuantity: string | number;
  lastOrderedAt: string;
  size?: 'sxs' | 'xs';
}) => {
  return (
    <div className="flex items-center">
      <Icon name="clock" />
      <span className={mergeClasses(text({ size }))}>
        {lastOrderedQuantity} on{' '}
        {dayjs(lastOrderedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
      </span>
    </div>
  );
};
