import { useQuery } from '@tanstack/react-query';
import { get } from '@/libs/utils/api';
import { queryKeys } from '@/libs/query/queryClient';
import { SavedItemType } from '@/types';

const fetchSavedItems = async (): Promise<{ savedItems: SavedItemType[] }> => {
  return get<{ savedItems: SavedItemType[] }>({
    url: `/saved-items`,
  });
};

export const useFetchSavedItems = () => {
  return useQuery({
    queryKey: queryKeys.savedItems.all,
    queryFn: () => fetchSavedItems(),
  });
};
