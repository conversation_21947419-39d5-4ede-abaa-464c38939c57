import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { post } from '@/libs/utils/api';
import { SavedItemType } from '@/types';
import { ApiErrorProps } from '@/types/utility';
import { apiErrorNotification } from '@/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';

const addSavedItem = async (cartItemId: string): Promise<SavedItemType> => {
  return post<SavedItemType>({
    url: `/saved-items/add`,
    body: {
      cartItemId,
    },
  });
};

export const useAddToSavedItems = () => {
  const queryClient = useQueryClient();
  const { fetchCart } = useCartStore();
  return useMutation({
    mutationFn: (cartItemId: string) => addSavedItem(cartItemId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.savedItems.all,
      });

      fetchCart();
    },
    onError: (error) => {
      const apiError = error as unknown as ApiErrorProps;
      const errorMessage =
        apiError.data?.message || 'Failed to add item to saved items';
      apiErrorNotification(errorMessage);
    },
  });
};
