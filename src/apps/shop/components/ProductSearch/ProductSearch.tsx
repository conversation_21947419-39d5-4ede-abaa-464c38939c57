import { ChangeEvent, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import { ProductType, SearchParamsProps } from '@/types';
import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import { buildQueryString } from '@/utils/buildQueryString';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';

import { useProductSuggestions } from './useProductSuggestions';
import { RadixCombobox } from '@/libs/ui/RadixCombobox';
import { Suggestions } from './Suggestions/Suggestions';
import { PreviouslyOrdered } from './PreviouslyOrdered/PreviouslyOrdered';
import clsx from 'clsx';

export const ProductSearchInput = () => {
  const { query, perPage, updateSearchQueryValue, getSearchProduct } =
    useProductStore();
  const { t } = useTranslation();
  const [selectedProduct, setSelectedProduct] = useState<string>('');

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [, setQueryParams] = useSearchParams();

  const { suggestions, previouslyOrderedItems, isSuggestionsLoading } =
    useProductSuggestions(query);

  useEffect(() => {
    setSelectedProduct('');
  }, [pathname]);

  const handleSearchQuery = async (value: string) => {
    const params = {
      query: value.trim(),
      page: 1,
      perPage,
      sortBy: undefined,
      sortOrder: undefined,
    };
    const newQuery = buildQueryString<SearchParamsProps<ProductType>>(params);

    if (pathname !== SHOP_ROUTES_PATH.search) {
      navigate(`${SHOP_ROUTES_PATH.search}?${newQuery}`);

      return;
    }
    getSearchProduct(params, setQueryParams);
  };

  const handleProductSelect = (value: unknown) => {
    const productName = value as string;
    if (productName) {
      setSelectedProduct(productName);
      updateSearchQueryValue(productName);
      handleSearchQuery(productName);
    }
  };

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    updateSearchQueryValue(value);
    setSelectedProduct('');
  };

  const handleEnterPress = (searchTerm: string) => {
    handleSearchQuery(searchTerm);
  };

  return (
    <RadixCombobox
      value={selectedProduct || query}
      onChange={handleProductSelect}
      isLoading={isSuggestionsLoading}
    >
      <RadixCombobox.Input
        placeholder={t('common.searchProducts')}
        onChange={handleInputChange}
        onEnterPress={handleEnterPress}
        displayValue={(value) => (value as string) || query}
        className="w-2xl rounded-lg"
      />

      {suggestions.length > 0 && (
        <RadixCombobox.Options className="flex px-6 py-4">
          <div
            className={clsx('flex flex-col pt-3', {
              'w-1/3': previouslyOrderedItems.length > 0,
              'w-full': previouslyOrderedItems.length === 0,
            })}
          >
            <p className="mb-1 text-sm font-semibold">Results</p>
            <Suggestions />
          </div>
          {previouslyOrderedItems.length > 0 && (
            <div className="my-2 flex w-2/3 flex-col gap-2 border-l border-gray-200 pl-4">
              <p className="mb-1 text-sm font-semibold">Buy again</p>
              <PreviouslyOrdered />
            </div>
          )}
        </RadixCombobox.Options>
      )}
    </RadixCombobox>
  );
};
