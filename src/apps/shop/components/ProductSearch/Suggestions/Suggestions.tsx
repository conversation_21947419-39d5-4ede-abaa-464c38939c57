import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';

import { RadixCombobox } from '@/libs/ui/RadixCombobox';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { useProductSuggestions } from '../useProductSuggestions';

export const Suggestions = () => {
  const { query } = useProductStore();
  const { suggestions } = useProductSuggestions(query);

  return (
    <>
      {suggestions.slice(0, 7).map((suggestion) => (
        <RadixCombobox.Option
          key={suggestion}
          value={suggestion}
          className="pl-0"
        >
          <Button variant="unstyled">
            <Icon
              name="magnifier"
              size="1rem"
              color="#B8B8B8"
              className="mr-3"
              aria-hidden={true}
            ></Icon>
            <span className="line-clamp-1 text-left text-sm">{suggestion}</span>
          </Button>
        </RadixCombobox.Option>
      ))}
    </>
  );
};
