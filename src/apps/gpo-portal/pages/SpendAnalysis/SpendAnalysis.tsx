import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { ClinicCard } from './components/ClinicCard/ClinicCard';
import { FilterControls } from './components/FilterControls/FilterControls';
import { NoResults } from '@/libs/ui/NoResults/NoResults';
import { useState, useMemo } from 'react';
import { useSpendAnalysis } from './hooks/useSpendAnalysis';
import { transformApiDataToClinicCard } from './utils/transformApiData';
import dayjs from 'dayjs';
import { FEATURE_FLAGS } from '@/constants';

export const SpendAnalysis = () => {
  const [selectedClinics, setSelectedClinics] = useState<Set<string>>(
    new Set(),
  );
  const [searchTerm, setSearchTerm] = useState('');

  const { data, isLoading, error, filters, updateFilters, goToPage, refresh } =
    useSpendAnalysis({
      per_page: 50,
      order_by: 'name',
      direction: 'asc',
    });

  const transformedClinics = useMemo(() => {
    if (!data?.data) return [];
    return data.data.map(transformApiDataToClinicCard);
  }, [data]);

  const filteredClinics = useMemo(() => {
    let filtered = transformedClinics.filter((clinicData) =>
      clinicData.clinic.name.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    if (filters.inactive_only) {
      filtered = filtered.filter((clinicData) => !clinicData.clinic.isActive);
    }

    return filtered;
  }, [transformedClinics, searchTerm, filters.inactive_only]);

  const allSelected =
    filteredClinics.length > 0 &&
    filteredClinics.every((clinicData) =>
      selectedClinics.has(clinicData.clinic.id),
    );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allClinicIds = new Set(
        filteredClinics.map((clinicData) => clinicData.clinic.id),
      );
      setSelectedClinics(allClinicIds);
    } else {
      setSelectedClinics(new Set());
    }
  };

  const handleSelectClinic = (clinicId: string, checked: boolean) => {
    const newSelected = new Set(selectedClinics);
    if (checked) {
      newSelected.add(clinicId);
    } else {
      newSelected.delete(clinicId);
    }
    setSelectedClinics(newSelected);
  };

  const handleClinicsPerPageChange = (perPage: number) => {
    updateFilters({ per_page: perPage });
  };

  const handleInactiveClinicsToggle = (inactiveOnly: boolean) => {
    updateFilters({ inactive_only: inactiveOnly });
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  const handleSort = (orderBy: string, direction: 'asc' | 'desc') => {
    updateFilters({
      order_by: orderBy as
        | 'name'
        | 'active_users'
        | 'inactive_users'
        | 'notifications'
        | 'total_spend'
        | 'preferred_vendor_percent',
      direction,
    });
  };

  const exportUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/gpo/spend-analysis/export?${new URLSearchParams(filters as Record<string, string>).toString()}`;

  if (error) {
    return (
      <div className="mt-4 rounded border border-red-200 bg-red-50 p-6">
        <h2 className="text-lg font-medium text-red-800">Error Loading Data</h2>
        <p className="mt-2 text-red-600">{error}</p>
        <Button variant="white" className="mt-4" onClick={refresh}>
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <>
      <h1 className="text-2xl font-medium">Spend Analysis Details</h1>
      <div className="mt-4 rounded border border-black/[0.04] bg-white p-6">
        <header className="mb-4 flex items-center justify-between">
          <h2 className="m-0 font-medium">
            Your Clinics ({data?.pagination?.total || 0})
          </h2>
          <div className="flex gap-3">
            <div className="min-w-[220px]">
              <Input
                placeholder="Search Clinics"
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                rightSection={
                  <Icon name="magnifier" size="16px" color="#C6C6C6" />
                }
              />
            </div>
            {FEATURE_FLAGS.SPEND_ANALYSIS_SORT && (
              <Button
                variant="white"
                p="0 1.5rem"
                onClick={() =>
                  handleSort(
                    'name',
                    filters.direction === 'asc' ? 'desc' : 'asc',
                  )
                }
              >
                <Icon name="sort" size="20px" />
                <span className="ml-1">Order by</span>
              </Button>
            )}
            {FEATURE_FLAGS.SPEND_ANALYSIS_FILTER && (
              <Button variant="white" p="0 1.5rem">
                <Icon name="settings" size="20px" />
                <span className="ml-1">Filter by</span>
              </Button>
            )}
            <Button
              variant="white"
              className="max-w-[56px]"
              href={exportUrl}
              download={`spend-analysis-${dayjs().format('YYYY-MM-DD')}.csv`}
            >
              <Icon name="download" size="16px" />
            </Button>
          </div>
        </header>
        <div className="rounded border border-black/[0.04] bg-[#F2F8FC] p-6">
          <div className="mb-4 flex items-center justify-between">
            <label className="flex gap-4 pl-4">
              <Checkbox
                checked={allSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
              <p className="m-0">Select all clinics</p>
            </label>

            <FilterControls
              clinicsPerPage={filters.per_page || 25}
              onClinicsPerPageChange={handleClinicsPerPageChange}
              inactiveClinicsOnly={filters.inactive_only || false}
              onInactiveClinicsToggle={handleInactiveClinicsToggle}
            />
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="text-gray-500">Loading clinics...</div>
            </div>
          ) : filteredClinics.length === 0 ? (
            <div className="py-8">
              <NoResults
                label={
                  searchTerm
                    ? `No clinics found matching "${searchTerm}"`
                    : filters.inactive_only
                      ? 'No inactive clinics found'
                      : 'No clinics found'
                }
              />
            </div>
          ) : (
            <>
              <div className="flex flex-col gap-2">
                {filteredClinics.map((clinicData) => (
                  <ClinicCard
                    key={clinicData.clinic.id}
                    clinic={clinicData.clinic}
                    spendAnalysis={clinicData.spendAnalysis}
                    isSelected={selectedClinics.has(clinicData.clinic.id)}
                    onSelect={(checked: boolean) =>
                      handleSelectClinic(clinicData.clinic.id, checked)
                    }
                  />
                ))}
              </div>

              {data?.pagination && data.pagination.last_page > 1 && (
                <div className="mt-6 flex justify-center">
                  <div className="flex gap-2">
                    <Button
                      variant="white"
                      onClick={() => goToPage(data.pagination.current_page - 1)}
                      disabled={data.pagination.current_page <= 1}
                    >
                      Previous
                    </Button>
                    <span className="flex items-center px-4">
                      Page {data.pagination.current_page} of{' '}
                      {data.pagination.last_page}
                    </span>
                    <Button
                      variant="white"
                      onClick={() => goToPage(data.pagination.current_page + 1)}
                      disabled={
                        data.pagination.current_page >=
                        data.pagination.last_page
                      }
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};
