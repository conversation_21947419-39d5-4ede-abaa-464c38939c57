import { useState, useEffect, useCallback } from 'react';
import { get } from '@/libs/utils/api';
import { GpoSpendAnalysisResponse, SpendAnalysisFilters } from '../types';

export const useSpendAnalysis = (initialFilters: SpendAnalysisFilters = {}) => {
  const [data, setData] = useState<GpoSpendAnalysisResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<SpendAnalysisFilters>({
    page: 1,
    per_page: 25,
    order_by: 'name',
    direction: 'asc',
    distributors_limit: 3,
    vendors_limit: 3,
    category_vendors_limit: 3,
    ...initialFilters,
  });

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => queryParams.append(key + '[]', v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });

      const url = `/gpo/spend-analysis${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      const response = await get<GpoSpendAnalysisResponse>({
        url,
        withApi: true,
      });

      setData(response);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to fetch spend analysis data';
      setError(errorMessage);
      console.error('Error fetching spend analysis:', err);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  const updateFilters = useCallback(
    (newFilters: Partial<SpendAnalysisFilters>) => {
      setFilters((prev) => ({
        ...prev,
        ...newFilters,
        page: 1, // Reset to first page when filters change
      }));
    },
    [],
  );

  const goToPage = useCallback((page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  }, []);

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    filters,
    updateFilters,
    goToPage,
    refresh,
  };
};
